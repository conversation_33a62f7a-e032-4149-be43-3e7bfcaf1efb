import Versions from './components/Versions'
import planfulyLogo from './assets/logo/Planfuly Logo.png'

import {
  Box,
  Button,
  Typography,
  Container,
  Stack,
  IconButton,
  Tooltip,
  Card,
  CardContent
} from '@mui/material'
import { styled, useTheme } from '@mui/material/styles'
import { Brightness4, Brightness7 } from '@mui/icons-material'
import { useThemeContext } from './components/ThemeWrapper'

// Styled components using the theme system
const LogoImage = styled('img')(({ theme }) => ({
  marginBottom: theme.spacing(3),
  height: 128,
  willChange: 'filter',
  transition: theme.transitions.create('filter', {
    duration: theme.transitions.duration.standard,
  }),
  '&:hover': {
    filter: `drop-shadow(0 0 1.2em ${theme.palette.primary.main}aa)`,
  },
}))

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.custom.borderRadius.medium,
  textTransform: 'none',
  fontWeight: theme.typography.fontWeightMedium,
  padding: theme.spacing(1.5, 3),
  boxShadow: theme.custom.shadows.button,
  '&:hover': {
    boxShadow: theme.shadows[4],
    transform: 'translateY(-2px)',
  },
}))

const ActionCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(1),
  borderRadius: theme.custom.borderRadius.medium,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[4],
  },
}))

const ActionLink = styled('a')(({ theme }) => ({
  cursor: 'pointer',
  textDecoration: 'none',
  display: 'inline-block',
  border: `2px solid ${theme.palette.primary.main}`,
  textAlign: 'center',
  fontWeight: theme.typography.fontWeightMedium,
  whiteSpace: 'nowrap',
  borderRadius: theme.custom.borderRadius.large,
  padding: theme.spacing(1, 2.5),
  fontSize: '0.875rem',
  color: theme.palette.primary.main,
  backgroundColor: 'transparent',
  transition: theme.transitions.create(['background-color', 'color', 'transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    transform: 'translateY(-1px)',
  },
}))


function App(): React.JSX.Element {
  const theme = useTheme()
  const { toggleMode, isDark } = useThemeContext()
  const ipcHandle = (): void => window.electron.ipcRenderer.send('ping')

  return (
    <Container maxWidth="md">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          py: 4,
        }}
      >
        {/* Theme Toggle Button */}
        <Box sx={{ position: 'absolute', top: 16, right: 16 }}>
          <Tooltip title={`Switch to ${isDark ? 'light' : 'dark'} mode`}>
            <IconButton onClick={toggleMode} color="primary">
              {isDark ? <Brightness7 /> : <Brightness4 />}
            </IconButton>
          </Tooltip>
        </Box>

        {/* Logo */}
        <LogoImage alt="Planfuly Logo" src={planfulyLogo} />

        {/* Title and Description */}
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ mb: 2, fontWeight: 'medium' }}
        >
          Powered by electron-vite
        </Typography>

        <Typography
          variant="h4"
          component="h1"
          align="center"
          sx={{
            mb: 2,
            fontWeight: 'bold',
            background: theme.custom.gradients.primary,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Build an Electron app with{' '}
          <Box
            component="span"
            sx={{
              background: 'linear-gradient(315deg, #087ea4 55%, #7c93ee)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
            }}
          >
            React
          </Box>
          {' '}and{' '}
          <Box
            component="span"
            sx={{
              background: 'linear-gradient(315deg, #3178c6 45%, #f0dc4e)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
            }}
          >
            TypeScript
          </Box>
        </Typography>

        <Typography
          variant="body1"
          color="text.secondary"
          align="center"
          sx={{ mb: 4 }}
        >
          Please try pressing <Typography component="code" sx={{
            fontFamily: 'monospace',
            backgroundColor: theme.palette.action.hover,
            px: 1,
            py: 0.5,
            borderRadius: 1,
          }}>F12</Typography> to open the devTool
        </Typography>

        {/* Action Buttons */}
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 4 }}>
          <ActionCard>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <ActionLink href="https://electron-vite.org/" target="_blank" rel="noreferrer">
                Documentation
              </ActionLink>
            </CardContent>
          </ActionCard>

          <ActionCard>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <ActionLink onClick={ipcHandle} style={{ cursor: 'pointer' }}>
                Send IPC
              </ActionLink>
            </CardContent>
          </ActionCard>

          <ActionCard>
            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
              <StyledButton variant="contained" color="primary">
                Themed Button
              </StyledButton>
            </CardContent>
          </ActionCard>
        </Stack>

        {/* Versions Component */}
        <Versions />
      </Box>
    </Container>
  )
}

export default App
